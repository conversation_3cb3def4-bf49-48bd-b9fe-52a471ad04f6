#!/usr/bin/env node

/**
 * Migration Generator from Models
 * Generates sequential migrations based on Sequelize models
 */

const fs = require('fs');
const path = require('path');
const { analyzeModels } = require('./analyze-models');

// Configuration
const MIGRATIONS_OUTPUT_PATH = path.join(__dirname, '../src/database/migrations-generated');
const BASE_TIMESTAMP = '20240101000000'; // Base timestamp for sequential numbering

console.log('🚀 Generating Migrations from Models...');
console.log('=====================================');

// Extended model schemas with more details
const EXTENDED_MODEL_SCHEMAS = {
  'Departments': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    name: { type: 'STRING(150)', allowNull: false },
    description: { type: 'STRING(500)', allowNull: true },
    company_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Companies', key: 'id' } },
    isActive: { type: 'BOOLEAN', defaultValue: true },
    isDeleted: { type: 'BOOLEAN', defaultValue: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true },
    deletedBy: { type: 'UUID', allowNull: true }
  },
  'Users': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    firstName: { type: 'STRING(150)', allowNull: false },
    lastName: { type: 'STRING(150)', allowNull: false },
    email: { type: 'STRING(150)', allowNull: false, unique: true },
    password: { type: 'STRING(255)', allowNull: false },
    roleId: { type: 'UUID', allowNull: false, foreignKey: { table: 'Roles', key: 'id' } },
    company_id: { type: 'UUID', allowNull: true, foreignKey: { table: 'Companies', key: 'id' } },
    department_id: { type: 'UUID', allowNull: true, foreignKey: { table: 'Departments', key: 'id' } },
    supervisor_id: { type: 'UUID', allowNull: true }, // Self-referencing, added separately
    refreshToken: { type: 'STRING', allowNull: true },
    isTemporaryPassword: { type: 'BOOLEAN', defaultValue: false },
    isActive: { type: 'BOOLEAN', defaultValue: true },
    isDeleted: { type: 'BOOLEAN', defaultValue: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true },
    deletedBy: { type: 'UUID', allowNull: true }
  },
  'Questions': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    question_text: { type: 'TEXT', allowNull: false },
    isActive: { type: 'BOOLEAN', defaultValue: true },
    isDeleted: { type: 'BOOLEAN', defaultValue: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true },
    deletedBy: { type: 'UUID', allowNull: true }
  },
  'QuestionOptions': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    question_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Questions', key: 'id' } },
    option_text: { type: 'TEXT', allowNull: false },
    option_value: { type: 'INTEGER', allowNull: false },
    category_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'AnswerCategories', key: 'id' } },
    isActive: { type: 'BOOLEAN', defaultValue: true },
    isDeleted: { type: 'BOOLEAN', defaultValue: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true },
    deletedBy: { type: 'UUID', allowNull: true }
  },
  'QuestionTranslations': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    question_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Questions', key: 'id' } },
    language_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Languages', key: 'id' } },
    translated_text: { type: 'TEXT', allowNull: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true }
  },
  'QuestionOptionTranslations': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    question_option_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'QuestionOptions', key: 'id' } },
    language_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Languages', key: 'id' } },
    translated_text: { type: 'TEXT', allowNull: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true }
  },
  'CompanyQuestionMaps': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    company_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Companies', key: 'id' } },
    question_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Questions', key: 'id' } },
    isActive: { type: 'BOOLEAN', defaultValue: true },
    isDeleted: { type: 'BOOLEAN', defaultValue: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true },
    deletedBy: { type: 'UUID', allowNull: true }
  },
  'SurveyResponses': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    user_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Users', key: 'id' } },
    company_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Companies', key: 'id' } },
    question_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Questions', key: 'id' } },
    selected_option_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'QuestionOptions', key: 'id' } },
    survey_session_id: { type: 'UUID', allowNull: false },
    response_date: { type: 'DATEONLY', allowNull: false },
    supervisor_id: { type: 'UUID', allowNull: true, foreignKey: { table: 'Users', key: 'id' } },
    department: { type: 'STRING(150)', allowNull: true },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true }
  },
  'Reports': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    name: { type: 'STRING(255)', allowNull: false },
    type: { type: 'ENUM', values: ['DAILY_SUMMARY', 'WEEKLY_SUMMARY', 'MONTHLY_SUMMARY', 'DEPARTMENT_ANALYSIS', 'TREND_ANALYSIS', 'CUSTOM_RANGE'], allowNull: false },
    format: { type: 'ENUM', values: ['PDF', 'EXCEL', 'CSV', 'JSON'], allowNull: false, defaultValue: 'JSON' },
    company_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'Companies', key: 'id' } },
    requested_by: { type: 'UUID', allowNull: false, foreignKey: { table: 'Users', key: 'id' } },
    start_date: { type: 'DATE', allowNull: false },
    end_date: { type: 'DATE', allowNull: false },
    parameters: { type: 'JSON', allowNull: true },
    status: { type: 'ENUM', values: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'], allowNull: false, defaultValue: 'PENDING' },
    file_path: { type: 'STRING(500)', allowNull: true },
    file_size: { type: 'INTEGER', allowNull: true },
    processing_started_at: { type: 'DATE', allowNull: true },
    processing_completed_at: { type: 'DATE', allowNull: true },
    error_message: { type: 'TEXT', allowNull: true },
    expires_at: { type: 'DATE', allowNull: true },
    download_count: { type: 'INTEGER', allowNull: false, defaultValue: 0 },
    last_downloaded_at: { type: 'DATE', allowNull: true },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true }
  },
  'ContactSubmissions': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    firstName: { type: 'STRING(100)', allowNull: false },
    lastName: { type: 'STRING(100)', allowNull: false },
    email: { type: 'STRING(255)', allowNull: false },
    phone: { type: 'STRING(20)', allowNull: true },
    subject: { type: 'STRING(255)', allowNull: false },
    message: { type: 'TEXT', allowNull: false },
    ipAddress: { type: 'STRING(45)', allowNull: true },
    userAgent: { type: 'STRING(500)', allowNull: true },
    refererDomain: { type: 'STRING(255)', allowNull: true },
    status: { type: 'ENUM', values: ['new', 'read', 'replied', 'closed'], defaultValue: 'new' },
    isSpam: { type: 'BOOLEAN', defaultValue: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    isDeleted: { type: 'BOOLEAN', defaultValue: false },
    deletedAt: { type: 'DATE', allowNull: true }
  }
};

async function generateMigrations() {
  try {
    console.log('🔍 Step 1: Analyzing models...');
    const analysis = analyzeModels();
    
    console.log('📁 Step 2: Creating output directory...');
    if (!fs.existsSync(MIGRATIONS_OUTPUT_PATH)) {
      fs.mkdirSync(MIGRATIONS_OUTPUT_PATH, { recursive: true });
    }

    console.log('🏗️ Step 3: Generating migration files...');
    
    let migrationCount = 0;
    const generatedFiles = [];

    for (const model of analysis.orderedModels) {
      migrationCount++;
      const timestamp = generateTimestamp(migrationCount);
      const fileName = `${timestamp}-create-${model.tableName.toLowerCase().replace(/([A-Z])/g, '-$1').substring(1)}-table.js`;
      const filePath = path.join(MIGRATIONS_OUTPUT_PATH, fileName);
      
      console.log(`  📝 Generating: ${fileName}`);
      
      // Get schema from extended schemas or fallback to analysis
      const schema = EXTENDED_MODEL_SCHEMAS[model.tableName] || model.schema;
      
      const migrationContent = generateMigrationContent(model.tableName, schema);
      fs.writeFileSync(filePath, migrationContent);
      
      generatedFiles.push(fileName);
    }

    console.log('\n✅ Migration generation complete!');
    console.log(`📊 Generated ${migrationCount} migration files in: ${MIGRATIONS_OUTPUT_PATH}`);
    console.log('\n📋 Generated files:');
    generatedFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file}`);
    });

    return { success: true, files: generatedFiles, outputPath: MIGRATIONS_OUTPUT_PATH };

  } catch (error) {
    console.error('❌ Migration generation failed:', error.message);
    throw error;
  }
}

function generateTimestamp(sequence) {
  const baseNum = parseInt(BASE_TIMESTAMP);
  const seqNum = baseNum + sequence;
  return seqNum.toString();
}

function generateMigrationContent(tableName, schema) {
  const columns = generateColumnDefinitions(schema);
  const indexes = generateIndexDefinitions(tableName, schema);
  const foreignKeys = generateForeignKeyConstraints(tableName, schema);
  
  return `'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🚀 Creating ${tableName} table...');
    
    try {
      await queryInterface.createTable('${tableName}', {
${columns}
      });
      console.log('✅ ${tableName} table created successfully');

${indexes}${foreignKeys}
      console.log('🎉 ${tableName} migration completed!');
    } catch (error) {
      console.error('❌ Error creating ${tableName} table:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('🔄 Rolling back ${tableName} table...');
    
    try {
      await queryInterface.dropTable('${tableName}');
      console.log('✅ ${tableName} table dropped successfully');
    } catch (error) {
      console.error('❌ Error dropping ${tableName} table:', error);
      throw error;
    }
  }
};`;
}

function generateColumnDefinitions(schema) {
  const columns = [];

  for (const [columnName, columnDef] of Object.entries(schema)) {
    let columnStr = `        ${columnName}: {\n`;

    // Handle data type
    if (columnDef.type === 'UUID') {
      columnStr += `          type: Sequelize.UUID,\n`;
      if (columnDef.defaultValue === 'UUIDV4') {
        columnStr += `          defaultValue: Sequelize.literal('(NEWID())'),\n`;
      }
    } else if (columnDef.type === 'DATE') {
      columnStr += `          type: Sequelize.DATE,\n`;
      if (columnName === 'createdAt' || columnName === 'updatedAt') {
        columnStr += `          defaultValue: Sequelize.literal('GETDATE()'),\n`;
      }
    } else if (columnDef.type === 'DATEONLY') {
      columnStr += `          type: Sequelize.DATEONLY,\n`;
    } else if (columnDef.type === 'BOOLEAN') {
      columnStr += `          type: Sequelize.BOOLEAN,\n`;
    } else if (columnDef.type === 'INTEGER') {
      columnStr += `          type: Sequelize.INTEGER,\n`;
    } else if (columnDef.type === 'TEXT') {
      columnStr += `          type: Sequelize.TEXT,\n`;
    } else if (columnDef.type === 'JSON') {
      columnStr += `          type: Sequelize.JSON,\n`;
    } else if (columnDef.type === 'ENUM') {
      const values = columnDef.values.map(v => `'${v}'`).join(', ');
      columnStr += `          type: Sequelize.ENUM(${values}),\n`;
    } else if (columnDef.type.startsWith('STRING')) {
      columnStr += `          type: Sequelize.${columnDef.type},\n`;
    } else {
      columnStr += `          type: Sequelize.STRING,\n`;
    }

    // Handle constraints
    if (columnDef.primaryKey) {
      columnStr += `          primaryKey: true,\n`;
    }

    if (columnDef.allowNull === false) {
      columnStr += `          allowNull: false,\n`;
    } else if (columnDef.allowNull === true) {
      columnStr += `          allowNull: true,\n`;
    }

    if (columnDef.unique) {
      columnStr += `          unique: true,\n`;
    }

    if (columnDef.defaultValue !== undefined && columnDef.defaultValue !== 'UUIDV4') {
      if (typeof columnDef.defaultValue === 'boolean') {
        columnStr += `          defaultValue: ${columnDef.defaultValue},\n`;
      } else if (typeof columnDef.defaultValue === 'number') {
        columnStr += `          defaultValue: ${columnDef.defaultValue},\n`;
      } else {
        columnStr += `          defaultValue: '${columnDef.defaultValue}',\n`;
      }
    }

    columnStr += `        }`;
    columns.push(columnStr);
  }

  return columns.join(',\n');
}

function generateIndexDefinitions(tableName, schema) {
  const indexes = [];

  // Add unique indexes
  for (const [columnName, columnDef] of Object.entries(schema)) {
    if (columnDef.unique && columnName !== 'id') {
      indexes.push(`
      console.log('  📌 Creating ${columnName} unique index...');
      await queryInterface.addIndex('${tableName}', ['${columnName}'], {
        name: 'idx_${tableName.toLowerCase()}_${columnName.toLowerCase()}',
        unique: true
      });`);
    }
  }

  // Add foreign key indexes
  for (const [columnName, columnDef] of Object.entries(schema)) {
    if (columnDef.foreignKey) {
      indexes.push(`
      console.log('  📌 Creating ${columnName} index...');
      await queryInterface.addIndex('${tableName}', ['${columnName}'], {
        name: 'idx_${tableName.toLowerCase()}_${columnName.toLowerCase()}'
      });`);
    }
  }

  // Add common composite indexes
  if (schema.isActive && schema.isDeleted) {
    indexes.push(`
      console.log('  📌 Creating active/deleted composite index...');
      await queryInterface.addIndex('${tableName}', ['isActive', 'isDeleted'], {
        name: 'idx_${tableName.toLowerCase()}_active_deleted'
      });`);
  }

  if (indexes.length > 0) {
    return `
      // Add indexes for performance
      console.log('🔍 Creating indexes for ${tableName} table...');${indexes.join('')}
      console.log('✅ All ${tableName} indexes created successfully');`;
  }

  return '';
}

function generateForeignKeyConstraints(tableName, schema) {
  const constraints = [];

  for (const [columnName, columnDef] of Object.entries(schema)) {
    if (columnDef.foreignKey && columnName !== 'supervisor_id') { // Handle supervisor_id separately
      const refTable = columnDef.foreignKey.table;
      const refKey = columnDef.foreignKey.key;

      constraints.push(`
      console.log('  🔗 Adding ${columnName} foreign key constraint...');
      await queryInterface.addConstraint('${tableName}', {
        fields: ['${columnName}'],
        type: 'foreign key',
        name: 'fk_${tableName.toLowerCase()}_${columnName.toLowerCase()}',
        references: {
          table: '${refTable}',
          field: '${refKey}'
        },
        onUpdate: 'CASCADE',
        onDelete: '${columnName.includes('company') || columnName.includes('user') ? 'NO ACTION' : 'CASCADE'}'
      });`);
    }
  }

  // Handle self-referencing foreign key for Users table
  if (tableName === 'Users' && schema.supervisor_id) {
    constraints.push(`
      console.log('  🔗 Adding supervisor_id self-referencing foreign key constraint...');
      await queryInterface.addConstraint('${tableName}', {
        fields: ['supervisor_id'],
        type: 'foreign key',
        name: 'fk_users_supervisor_id',
        references: {
          table: 'Users',
          field: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      });`);
  }

  if (constraints.length > 0) {
    return `
      // Add foreign key constraints
      console.log('🔗 Adding foreign key constraints for ${tableName}...');${constraints.join('')}
      console.log('✅ All ${tableName} foreign key constraints added successfully');`;
  }

  return '';
}

// Run generator
if (require.main === module) {
  generateMigrations()
    .then(result => {
      console.log('\n🎉 Success! You can now run:');
      console.log('   npm run db:migrate:sequential');
    })
    .catch(error => {
      console.error('\n💥 Generation failed:', error.message);
      process.exit(1);
    });
}

module.exports = { generateMigrations };

function generateColumnDefinitions(schema) {
  const columns = [];

  for (const [columnName, columnDef] of Object.entries(schema)) {
    let columnStr = `        ${columnName}: {\n`;

    // Handle data type
    if (columnDef.type === 'UUID') {
      columnStr += `          type: Sequelize.UUID,\n`;
      if (columnDef.defaultValue === 'UUIDV4') {
        columnStr += `          defaultValue: Sequelize.literal('(NEWID())'),\n`;
      }
    } else if (columnDef.type === 'DATE') {
      columnStr += `          type: Sequelize.DATE,\n`;
      if (columnName === 'createdAt' || columnName === 'updatedAt') {
        columnStr += `          defaultValue: Sequelize.literal('GETDATE()'),\n`;
      }
    } else if (columnDef.type === 'DATEONLY') {
      columnStr += `          type: Sequelize.DATEONLY,\n`;
    } else if (columnDef.type === 'BOOLEAN') {
      columnStr += `          type: Sequelize.BOOLEAN,\n`;
    } else if (columnDef.type === 'INTEGER') {
      columnStr += `          type: Sequelize.INTEGER,\n`;
    } else if (columnDef.type === 'TEXT') {
      columnStr += `          type: Sequelize.TEXT,\n`;
    } else if (columnDef.type === 'JSON') {
      columnStr += `          type: Sequelize.JSON,\n`;
    } else if (columnDef.type === 'ENUM') {
      const values = columnDef.values.map(v => `'${v}'`).join(', ');
      columnStr += `          type: Sequelize.ENUM(${values}),\n`;
    } else if (columnDef.type.startsWith('STRING')) {
      columnStr += `          type: Sequelize.${columnDef.type},\n`;
    } else {
      columnStr += `          type: Sequelize.STRING,\n`;
    }

    // Handle constraints
    if (columnDef.primaryKey) {
      columnStr += `          primaryKey: true,\n`;
    }

    if (columnDef.allowNull === false) {
      columnStr += `          allowNull: false,\n`;
    } else if (columnDef.allowNull === true) {
      columnStr += `          allowNull: true,\n`;
    }

    if (columnDef.unique) {
      columnStr += `          unique: true,\n`;
    }

    if (columnDef.defaultValue !== undefined && columnDef.defaultValue !== 'UUIDV4') {
      if (typeof columnDef.defaultValue === 'boolean') {
        columnStr += `          defaultValue: ${columnDef.defaultValue},\n`;
      } else if (typeof columnDef.defaultValue === 'number') {
        columnStr += `          defaultValue: ${columnDef.defaultValue},\n`;
      } else {
        columnStr += `          defaultValue: '${columnDef.defaultValue}',\n`;
      }
    }

    columnStr += `        }`;
    columns.push(columnStr);
  }

  return columns.join(',\n');
}

function generateIndexDefinitions(tableName, schema) {
  const indexes = [];

  // Add primary key index (automatic)
  // Add unique indexes
  for (const [columnName, columnDef] of Object.entries(schema)) {
    if (columnDef.unique && columnName !== 'id') {
      indexes.push(`
      console.log('  📌 Creating ${columnName} unique index...');
      await queryInterface.addIndex('${tableName}', ['${columnName}'], {
        name: 'idx_${tableName.toLowerCase()}_${columnName.toLowerCase()}',
        unique: true
      });`);
    }
  }

  // Add foreign key indexes
  for (const [columnName, columnDef] of Object.entries(schema)) {
    if (columnDef.foreignKey) {
      indexes.push(`
      console.log('  📌 Creating ${columnName} index...');
      await queryInterface.addIndex('${tableName}', ['${columnName}'], {
        name: 'idx_${tableName.toLowerCase()}_${columnName.toLowerCase()}'
      });`);
    }
  }

  // Add common composite indexes
  if (schema.isActive && schema.isDeleted) {
    indexes.push(`
      console.log('  📌 Creating active/deleted composite index...');
      await queryInterface.addIndex('${tableName}', ['isActive', 'isDeleted'], {
        name: 'idx_${tableName.toLowerCase()}_active_deleted'
      });`);
  }

  if (indexes.length > 0) {
    return `
      // Add indexes for performance
      console.log('🔍 Creating indexes for ${tableName} table...');${indexes.join('')}
      console.log('✅ All ${tableName} indexes created successfully');`;
  }

  return '';
}

function generateForeignKeyConstraints(tableName, schema) {
  const constraints = [];

  for (const [columnName, columnDef] of Object.entries(schema)) {
    if (columnDef.foreignKey && columnName !== 'supervisor_id') { // Handle supervisor_id separately
      const refTable = columnDef.foreignKey.table;
      const refKey = columnDef.foreignKey.key;

      constraints.push(`
      console.log('  🔗 Adding ${columnName} foreign key constraint...');
      await queryInterface.addConstraint('${tableName}', {
        fields: ['${columnName}'],
        type: 'foreign key',
        name: 'fk_${tableName.toLowerCase()}_${columnName.toLowerCase()}',
        references: {
          table: '${refTable}',
          field: '${refKey}'
        },
        onUpdate: 'CASCADE',
        onDelete: '${columnName.includes('company') || columnName.includes('user') ? 'NO ACTION' : 'CASCADE'}'
      });`);
    }
  }

  // Handle self-referencing foreign key for Users table
  if (tableName === 'Users' && schema.supervisor_id) {
    constraints.push(`
      console.log('  🔗 Adding supervisor_id self-referencing foreign key constraint...');
      await queryInterface.addConstraint('${tableName}', {
        fields: ['supervisor_id'],
        type: 'foreign key',
        name: 'fk_users_supervisor_id',
        references: {
          table: 'Users',
          field: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      });`);
  }

  if (constraints.length > 0) {
    return `
      // Add foreign key constraints
      console.log('🔗 Adding foreign key constraints for ${tableName}...');${constraints.join('')}
      console.log('✅ All ${tableName} foreign key constraints added successfully');`;
  }

  return '';
}

// Run generator
if (require.main === module) {
  generateMigrations()
    .then(result => {
      console.log('\n🎉 Success! You can now run:');
      console.log('   npm run db:migrate:sequential');
    })
    .catch(error => {
      console.error('\n💥 Generation failed:', error.message);
      process.exit(1);
    });
}

module.exports = { generateMigrations };

