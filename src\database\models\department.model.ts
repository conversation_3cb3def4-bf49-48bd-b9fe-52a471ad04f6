import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  AllowNull,
  ForeignKey,
  BelongsTo,
  CreatedAt,
  UpdatedAt,
  HasMany,
} from 'sequelize-typescript';
import { Company } from './company.model';
import { User } from './user.model';

@Table({
  tableName: 'Departments',
  timestamps: true,
})
export class Department extends Model<Department> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @AllowNull(false)
  @Column({ type: DataType.STRING(150) })
  name: string;

  @AllowNull(true)
  @Column({ type: DataType.STRING(500) })
  description: string;

  @ForeignKey(() => Company)
  @AllowNull(false)
  @Column(DataType.UUID)
  company_id: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  isDeleted: boolean;

  @Column(DataType.UUID)
  deletedBy: string;

  // Associations
  @BelongsTo(() => Company)
  company: Company;

  @HasMany(() => User, 'department_id')
  users: User[];
}
