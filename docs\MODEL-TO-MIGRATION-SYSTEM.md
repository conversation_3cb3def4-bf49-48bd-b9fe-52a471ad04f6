# Model-to-Migration Generation System

## 🎯 Overview

This system automatically generates sequential database migrations from your Sequelize models, ensuring proper dependency order and SQL Server compatibility.

## 📁 System Components

### **1. Model Analyzer** (`scripts/analyze-models.js`)
- Scans your `src/database/models/` directory
- Analyzes model dependencies and relationships
- Creates dependency-resolved execution order
- Outputs analysis to `temp/model-analysis.json`

### **2. Migration Generator** (`scripts/generate-migrations-from-models.js`)
- Reads model analysis and generates migration files
- Creates timestamped migrations in correct sequence
- Handles foreign key constraints properly
- Removes SQL Server incompatible `comment` properties
- Outputs to `src/database/migrations-generated/`

### **3. Sequential Runner** (`scripts/run-migrations-sequential.js`)
- Runs migrations in dependency order
- Provides detailed logging and error handling
- Supports rollback operations
- Works with both generated and existing migrations

## 🚀 Quick Start

### **Step 1: Add Scripts to package.json**
```json
{
  "scripts": {
    "analyze:models": "node scripts/analyze-models.js",
    "generate:migrations-from-models": "node scripts/generate-migrations-from-models.js",
    "db:migrate:sequential": "node scripts/run-migrations-sequential.js up",
    "db:setup:from-models": "npm run generate:migrations-from-models && npm run db:migrate:sequential"
  }
}
```

### **Step 2: Generate Migrations from Models**
```bash
# Analyze your models first (optional)
npm run analyze:models

# Generate all migrations from models
npm run generate:migrations-from-models
```

### **Step 3: Run Migrations Sequentially**
```bash
# Run all pending migrations
npm run db:migrate:sequential

# Or use the combined command
npm run db:setup:from-models
```

## 📋 Generated Migration Sequence

Based on your models, migrations are generated in this order:

1. **Roles** (no dependencies)
2. **Languages** (no dependencies)
3. **AnswerCategories** (no dependencies)
4. **ProductOwners** (depends on Roles)
5. **Companies** (depends on ProductOwners)
6. **Departments** (depends on Companies)
7. **Users** (depends on Roles, Companies, Departments)
8. **Questions** (no dependencies)
9. **QuestionOptions** (depends on Questions, AnswerCategories)
10. **QuestionTranslations** (depends on Questions, Languages)
11. **QuestionOptionTranslations** (depends on QuestionOptions, Languages)
12. **CompanyQuestionMaps** (depends on Companies, Questions)
13. **SurveyResponses** (depends on Users, Companies, Questions, QuestionOptions)
14. **Reports** (depends on Companies, Users)
15. **ContactSubmissions** (no dependencies)

## 🔧 Available Commands

### **Model Analysis**
```bash
npm run analyze:models                    # Analyze model dependencies
```

### **Migration Generation**
```bash
npm run generate:migrations-from-models  # Generate migrations from models
```

### **Migration Execution**
```bash
npm run db:migrate:sequential            # Run all pending migrations
npm run db:migrate:sequential:status     # Check migration status
npm run db:migrate:sequential:rollback   # Rollback last migration
npm run db:migrate:sequential:help       # Show help
```

### **Combined Operations**
```bash
npm run db:setup:from-models             # Generate + run migrations
```

## 🏗️ Generated Migration Features

### **✅ SQL Server Compatibility**
- No `comment` properties (prevents MS_Description errors)
- Proper `NEWID()` for UUID generation
- `GETDATE()` for timestamps
- Correct data type mappings

### **✅ Proper Foreign Key Handling**
- Foreign key constraints added separately from table creation
- Self-referencing relationships handled correctly (Users.supervisor_id)
- Appropriate CASCADE/SET NULL/NO ACTION rules

### **✅ Performance Optimized**
- Automatic index creation for foreign keys
- Unique indexes for unique columns
- Composite indexes for common query patterns
- Primary key indexes (automatic)

### **✅ Comprehensive Logging**
- Step-by-step progress tracking
- Error handling with detailed messages
- Success confirmations
- Rollback support

## 📊 Example Generated Migration

```javascript
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🚀 Creating Users table...');
    
    try {
      await queryInterface.createTable('Users', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.literal('(NEWID())'),
          primaryKey: true,
          allowNull: false,
        },
        firstName: {
          type: Sequelize.STRING(150),
          allowNull: false,
        },
        // ... other columns
      });
      console.log('✅ Users table created successfully');

      // Add indexes for performance
      console.log('🔍 Creating indexes for Users table...');
      await queryInterface.addIndex('Users', ['email'], {
        name: 'idx_users_email',
        unique: true
      });
      // ... other indexes

      // Add foreign key constraints
      console.log('🔗 Adding foreign key constraints for Users...');
      await queryInterface.addConstraint('Users', {
        fields: ['roleId'],
        type: 'foreign key',
        name: 'fk_users_roleid',
        references: { table: 'Roles', field: 'id' },
        onUpdate: 'CASCADE',
        onDelete: 'NO ACTION'
      });
      // ... other constraints

      console.log('🎉 Users migration completed!');
    } catch (error) {
      console.error('❌ Error creating Users table:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('🔄 Rolling back Users table...');
    try {
      await queryInterface.dropTable('Users');
      console.log('✅ Users table dropped successfully');
    } catch (error) {
      console.error('❌ Error dropping Users table:', error);
      throw error;
    }
  }
};
```

## 🔍 Troubleshooting

### **No migrations generated?**
- Check that your models exist in `src/database/models/`
- Ensure models have proper `@Table` decorators
- Verify model file names end with `.model.ts`

### **Foreign key errors?**
- Check that referenced tables are created first
- Verify foreign key column names match exactly
- Ensure proper data types (UUID, etc.)

### **Migration order issues?**
- Review the dependency mapping in `analyze-models.js`
- Check for circular dependencies
- Verify model relationships are correctly defined

### **SQL Server compatibility issues?**
- All `comment` properties are automatically removed
- UUID generation uses `NEWID()` instead of `UUIDV4`
- Timestamps use `GETDATE()` for defaults

## 🎯 Benefits

✅ **No manual migration writing** - Generated from models
✅ **Proper dependency order** - Automatic resolution
✅ **SQL Server compatible** - No compatibility issues
✅ **Foreign key safe** - Constraints added separately
✅ **Performance optimized** - Automatic indexing
✅ **Error handling** - Comprehensive logging
✅ **Rollback support** - Safe migration management
✅ **Keep sync disabled** - Full migration control

## 📝 Next Steps

1. **Add the scripts** to your `package.json`
2. **Generate migrations** from your models
3. **Run migrations** sequentially
4. **Verify database** schema matches your models

Your database will be created exactly as defined in your Sequelize models, with proper relationships, indexes, and constraints!
