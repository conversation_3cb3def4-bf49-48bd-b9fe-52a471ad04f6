// src/core/database/models/user.model.ts
import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  ForeignKey,
  BelongsTo,
  HasMany,
  AllowNull,
} from 'sequelize-typescript';
import { Company } from './company.model';
import { Role } from './role.model';
import { Department } from './department.model';
import { SurveyResponse } from './survey-response.model';


@Table({
  tableName: 'Users',
  timestamps: false,

})
export class User extends Model<User> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @Column({ type: DataType.STRING(150), allowNull: false })
  firstName: string;

  @Column({ type: DataType.STRING(150), allowNull: false })
  lastName: string;

  @Column({ type: DataType.STRING(150), allowNull: false, unique: true })
  email: string;

  @Column({ type: DataType.STRING(255), allowNull: false })
  password: string;

  @ForeignKey(() => Role)
  @AllowNull(false)
  @Column(DataType.UUID)
  roleId: string;

  @ForeignKey(() => Company)
  @Column({ type: DataType.UUID })
  company_id: string;

  @Column({ type: DataType.UUID, allowNull: true })
  supervisor_id: string; // Only applicable for employee, points to supervisor user

  @ForeignKey(() => Department)
  @Column({ type: DataType.UUID, allowNull: true })
  department_id: string;

  @Default(true)
  @Column({ type: DataType.BOOLEAN })
  isActive: boolean;

  @Default(false)
  @Column({ type: DataType.BOOLEAN })
  isDeleted: boolean;

  @Default(false)
  @Column({ type: DataType.BOOLEAN })
  isTemporaryPassword: boolean;

  @Column({ type: DataType.DATE })
  declare createdAt: Date;

  @Column({ type: DataType.UUID })
  createdBy: string;

  @Column({ type: DataType.DATE })
  declare updatedAt: Date;

  @Column({ type: DataType.UUID })
  updatedBy: string;

  @Column({ type: DataType.UUID })
  deletedBy: string;

  @BelongsTo(() => Company)
  company: Company;

  @BelongsTo(() => Role)
  role: Role;

  @BelongsTo(() => Department)
  department: Department;

  @BelongsTo(() => User, 'supervisor_id')
  supervisor: User;

  @HasMany(() => SurveyResponse, 'user_id')
  surveyResponses: SurveyResponse[];

  @AllowNull(true)
  @Column(DataType.STRING)
  refreshToken: string | null;
}
