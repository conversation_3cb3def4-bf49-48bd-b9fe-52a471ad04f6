import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { CorsMiddleware } from './common/middleware/cors.middleware';
import { LanguageMiddleware } from './common/middleware/language.middleware';
import { SequelizeModule } from '@nestjs/sequelize';
import { sequelizeConfig } from './config/sequelize.config';
import { AuthModule } from './auth/auth.module';
import { ProductOwnerModule } from './product-owner/product-owner.module';
import { ReportsModule } from './reports/reports.module';
import { CategoriesModule } from './categories/categories.module';
import { SurveyModule } from './survey/survey.module';
import { DepartmentsModule } from './departments/departments.module';
import { UsersModule } from './users/users.module';
import { RolesModule } from './roles/roles.module';
import { QuestionsModule } from './questions/questions.module';
import { CommonModule } from './common/common.module';
import { ContactModule } from './contact/contact.module';
// Import your models
import { User } from './database/models/user.model';
import { Role } from './database/models/role.model';
import { Company } from './database/models/company.model';
import { ProductOwner } from './database/models/product-owner-model';
import { Question } from './database/models/question.model';
import { QuestionOption } from './database/models/question-option.model';
import { CompanyQuestionMap } from './database/models/company-question-map.model';
import { AnswerCategory } from './database/models/answer-category.model';
import { SurveyResponse } from './database/models/survey-response.model';
import { Report } from './database/models/report.model';
import { Department } from './database/models/department.model';
import { Language } from './database/models/language.model';
import { QuestionTranslation } from './database/models/question-translation.model';
import { QuestionOptionTranslation } from './database/models/question-option-translation.model';
import { ContactSubmission } from './database/models/contact-submission.model';

@Module({
  imports: [
    SequelizeModule.forRoot({
      ...sequelizeConfig,
      models: [
        User,
        Role,
        Company,
        ProductOwner,
        Question,
        QuestionOption,
        CompanyQuestionMap,
        AnswerCategory,
        SurveyResponse,
        Report,
        Department,
        Language,
        QuestionTranslation,
        QuestionOptionTranslation,
        ContactSubmission
      ],
    }),
    AuthModule,
    ProductOwnerModule,
    ReportsModule,
    CategoriesModule,
    SurveyModule,
    DepartmentsModule,
    UsersModule,
    RolesModule,
    QuestionsModule,
    CommonModule,
    ContactModule,
    // Other modules
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CorsMiddleware, LanguageMiddleware)
      .forRoutes('*');
  }
}
