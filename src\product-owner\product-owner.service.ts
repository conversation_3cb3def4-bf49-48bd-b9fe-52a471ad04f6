import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

import { CreationAttributes } from 'sequelize';
import { Company } from 'src/database/models/company.model';
import { CreateCompanyDto } from './dto/create-company-dto';
import { User } from 'src/database/models/user.model';
import { Role } from 'src/database/models/role.model'; // adjust the path if needed
import * as bcrypt from 'bcrypt';

@Injectable()
export class ProductOwnerService {
  // constructor(
  //   @InjectModel(Company)
  //   private readonly companyModel: typeof Company,
  // ) {}

  constructor(
    @InjectModel(Company) private companyModel: typeof Company,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(Role) private roleModel: typeof Role, // 👈 Inject Role model
    // private mailService: MailService,
  ) { }

  async createCompany(dto: CreateCompanyDto): Promise<{ message: string, company: Company }> {
    // Optional: Add extra metadata like createdBy from the logged-in user
    const company = await this.companyModel.create(dto as CreationAttributes<Company>);

    const tempPassword = Math.random().toString(36).slice(-8);
    // const hashedPassword = await bcrypt.hash(tempPassword, 10);

    const hashedPassword = await bcrypt.hash('umavanshi@1101', 10);

    const superAdminRole = await this.roleModel.findOne({
      where: { name: 'CompanyAdmin', isDeleted: false },
    });

    if (!superAdminRole) {
      throw new Error('SuperAdmin role not found');
    }

    // Fix: Use the correct field names from your User model
    const adminUser = await this.userModel.create({
      firstName: dto.contact_person_firstName,
      lastName: dto.contact_person_lastName,
      email: dto.contact_person_email,
      password: hashedPassword,
      roleId: superAdminRole.id,
      isActive: true,
      company_id: company.id, // Link the user to the company
    } as CreationAttributes<User>);

    // Optionally, you can send an email to the admin user with the temporary password
    // await this.mailService.sendWelcomeEmail(adminUser.email, tempPassword);

    return { message: 'Company and admin user created successfully', company };
  }

  async getCompanies(): Promise<Company[]> {
    return this.companyModel.findAll({
      where: { isDeleted: false },
      order: [['createdAt', 'DESC']]
    });
  }

  async getCompanyById(id: string): Promise<Company> {
    const company = await this.companyModel.findOne({
      where: { id, isDeleted: false }
    });

    if (!company) {
      throw new Error(`Company with ID ${id} not found`);
    }

    return company;
  }
}
