import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Question } from 'src/database/models/question.model';
import { QuestionOption } from 'src/database/models/question-option.model';
import { CompanyQuestionMap } from 'src/database/models/company-question-map.model';
import { Company } from 'src/database/models/company.model';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { CreateQuestionOptionDto } from './dto/create-question-option.dto';
import { UpdateQuestionOptionDto } from './dto/update-question-option.dto';
import { AssignCompanyQuestionDto } from './dto/assign-company-question.dto';
import { CreationAttributes, Transaction } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';
import { TranslationService, TranslationData } from '../common/services/translation.service';

// Interfaces for plain objects (without Sequelize model methods)
export interface PlainQuestionOption {
  id: string;
  question_id: string;
  option_text: string;
  option_value: number;
  category_id: string;
  isActive: boolean;
  createdAt: Date;
  createdBy: string;
  updatedAt: Date;
  updatedBy: string;
  isDeleted: boolean;
  deletedBy: string;
}

export interface PlainCompanyQuestionMap {
  id: string;
  company_id: string;
  question_id: string;
  isActive: boolean;
  createdAt: Date;
  createdBy: string;
  updatedAt: Date;
  updatedBy: string;
  isDeleted: boolean;
  deletedBy: string;
}

export interface PlainQuestionTranslation {
  id: string;
  question_id: string;
  language_id: string;
  question_text: string;
  isActive: boolean;
  createdAt: Date;
  createdBy: string;
  updatedAt: Date;
  updatedBy: string;
  isDeleted: boolean;
  deletedBy: string;
}

export interface PlainQuestion {
  id: string;
  question_text: string;
  isActive: boolean;
  createdAt: Date;
  createdBy: string;
  updatedAt: Date;
  updatedBy: string;
  isDeleted: boolean;
  deletedBy: string;
  options: PlainQuestionOption[];
  companyMaps?: PlainCompanyQuestionMap[];
  translations?: PlainQuestionTranslation[];
}

@Injectable()
export class QuestionService {
  constructor(
    @InjectModel(Question)
    private readonly questionModel: typeof Question,
    @InjectModel(QuestionOption)
    private readonly questionOptionModel: typeof QuestionOption,
    @InjectModel(CompanyQuestionMap)
    private readonly companyQuestionMapModel: typeof CompanyQuestionMap,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    private readonly sequelize: Sequelize,
    private readonly translationService: TranslationService,
  ) {}

  // Question CRUD operations
  async getAllQuestions(languageCode: string = 'en') {
    const questions = await this.questionModel.findAll({
      where: { isDeleted: false },
      include: [
        {
          model: QuestionOption,
          where: { isDeleted: false },
          required: false,
        },
      ],
    });

    // Localize the questions
    return this.localizeQuestions(questions, languageCode);
  }

  async getQuestionById(id: string, languageCode: string = 'en'): Promise<PlainQuestion> {
    const question = await this.findQuestionById(id);

    // Localize the question
    const localizedQuestions = await this.localizeQuestions([question], languageCode);
    return localizedQuestions[0];
  }

  private async findQuestionById(id: string): Promise<Question> {
    const question = await this.questionModel.findOne({
      where: { id, isDeleted: false },
      include: [
        {
          model: QuestionOption,
          where: { isDeleted: false },
          required: false,
        },
      ],
    });

    if (!question) {
      throw new NotFoundException(`Question with ID ${id} not found`);
    }

    return question;
  }

  async createQuestion(createQuestionDto: CreateQuestionDto) {
    const transaction = await this.sequelize.transaction();

    try {
      // Handle question text - support both string and translation object
      let questionText: string;
      let questionTranslations: TranslationData | null = null;

      if (typeof createQuestionDto.question_text === 'string') {
        // Backward compatibility - treat as English text
        questionText = createQuestionDto.question_text;
        questionTranslations = { en: createQuestionDto.question_text };
      } else {
        // Multi-language object
        questionTranslations = createQuestionDto.question_text;
        // Validate that English translation exists
        if (!questionTranslations.en) {
          throw new BadRequestException('English (en) translation is required for question text');
        }
        questionText = questionTranslations.en; // Use English as default
      }

      // Validate that questionText is not empty
      if (!questionText || questionText.trim().length === 0) {
        throw new BadRequestException('Question text cannot be empty');
      }

      // Create the question
      const question = await this.questionModel.create(
        {
          question_text: questionText.trim(),
          createdBy: createQuestionDto.createdBy || '',
        } as CreationAttributes<Question>,
        { transaction },
      );

      // Create question translations (non-blocking)
      if (questionTranslations) {
        this.translationService.createQuestionTranslations(
          question.id,
          questionTranslations,
          createQuestionDto.createdBy
        ).catch(translationError => {
          console.warn('Warning: Failed to create question translations:', translationError.message);
        });
      }

      // Create the options
      if (createQuestionDto.options && createQuestionDto.options.length > 0) {
        for (const optionDto of createQuestionDto.options) {
          // Handle option text - support both string and translation object
          let optionText: string;
          let optionTranslations: TranslationData | null = null;

          if (typeof optionDto.option_text === 'string') {
            // Backward compatibility - treat as English text
            optionText = optionDto.option_text;
            optionTranslations = { en: optionDto.option_text };
          } else {
            // Multi-language object
            optionTranslations = optionDto.option_text;
            // Validate that English translation exists
            if (!optionTranslations.en) {
              throw new BadRequestException('English (en) translation is required for option text');
            }
            optionText = optionTranslations.en; // Use English as default
          }

          // Validate that optionText is not empty
          if (!optionText || optionText.trim().length === 0) {
            throw new BadRequestException('Option text cannot be empty');
          }

          // Validate categoryId exists (basic UUID format check)
          if (!optionDto.categoryId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(optionDto.categoryId)) {
            throw new BadRequestException('Valid categoryId is required for each option');
          }

          // Create the option
          const option = await this.questionOptionModel.create(
            {
              option_text: optionText.trim(),
              option_value: optionDto.option_value,
              category_id: optionDto.categoryId,
              question_id: question.id,
              createdBy: createQuestionDto.createdBy || '',
            } as CreationAttributes<QuestionOption>,
            { transaction }
          );

          // Create option translations (non-blocking)
          if (optionTranslations) {
            this.translationService.createQuestionOptionTranslations(
              option.id,
              optionTranslations,
              createQuestionDto.createdBy
            ).catch(translationError => {
              console.warn(`Warning: Failed to create option translations for option ${option.id}:`, translationError.message);
            });
          }
        }
      }

      await transaction.commit();

      // Return the created question with its options
      return this.getQuestionById(question.id);
    } catch (error) {
      await transaction.rollback();
      console.error("Error in question creation:", error);

      // Provide more specific error messages
      if (error instanceof BadRequestException) {
        throw error; // Re-throw validation errors as-is
      }

      // Handle specific database errors
      if (error.name === 'SequelizeValidationError') {
        throw new BadRequestException(`Validation error: ${error.message}`);
      }

      if (error.name === 'SequelizeForeignKeyConstraintError') {
        throw new BadRequestException('Invalid category ID provided for one or more options');
      }

      if (error.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestException('A question with similar data already exists');
      }

      // Generic error for unexpected issues
      throw new BadRequestException(`Failed to create question: ${error.message || 'Unknown error occurred'}`);
    }
  }

  async updateQuestion(id: string, updateQuestionDto: UpdateQuestionDto) {
    const question = await this.findQuestionById(id);

    await question.update({
      ...updateQuestionDto,
    });

    return this.getQuestionById(id);
  }

  async deleteQuestion(id: string, userId: string) {
    const question = await this.findQuestionById(id);

    await question.update({
      isDeleted: true,
      deletedBy: userId,
    });

    return { message: 'Question deleted successfully' };
  }

  // Question Option operations
  async addQuestionOption(questionId: string, createOptionDto: CreateQuestionOptionDto) {
    // Verify question exists
    await this.findQuestionById(questionId);

    // Handle option text - support both string and translation object
    let optionText: string;
    let optionTranslations: TranslationData | null = null;

    if (typeof createOptionDto.option_text === 'string') {
      // Backward compatibility - treat as English text
      optionText = createOptionDto.option_text;
      optionTranslations = { en: createOptionDto.option_text };
    } else {
      // Multi-language object
      optionTranslations = createOptionDto.option_text;
      // Validate that English translation exists
      if (!optionTranslations.en) {
        throw new BadRequestException('English (en) translation is required for option text');
      }
      optionText = optionTranslations.en; // Use English as default
    }

    // Validate that optionText is not empty
    if (!optionText || optionText.trim().length === 0) {
      throw new BadRequestException('Option text cannot be empty');
    }

    const option = await this.questionOptionModel.create({
      option_text: optionText.trim(),
      option_value: createOptionDto.option_value,
      category_id: createOptionDto.categoryId,
      question_id: questionId,
      createdBy: createOptionDto.createdBy,
    } as CreationAttributes<QuestionOption>);

    // Create option translations if provided
    if (optionTranslations) {
      try {
        await this.translationService.createQuestionOptionTranslations(
          option.id,
          optionTranslations,
          createOptionDto.createdBy
        );
      } catch (translationError) {
        console.warn(`Warning: Failed to create option translations for option ${option.id}:`, translationError.message);
        // Continue without translations rather than failing the entire operation
      }
    }

    return option;
  }

  async updateQuestionOption(questionId: string, optionId: string, updateOptionDto: UpdateQuestionOptionDto) {
    const option = await this.questionOptionModel.findOne({
      where: {
        id: optionId,
        question_id: questionId,
        isDeleted: false,
      },
    });

    if (!option) {
      throw new NotFoundException(`Option with ID ${optionId} not found for question ${questionId}`);
    }

    await option.update({
      ...updateOptionDto,
    });

    return option;
  }

  async deleteQuestionOption(questionId: string, optionId: string, userId: string) {
    const option = await this.questionOptionModel.findOne({
      where: {
        id: optionId,
        question_id: questionId,
        isDeleted: false,
      },
    });

    if (!option) {
      throw new NotFoundException(`Option with ID ${optionId} not found for question ${questionId}`);
    }

    await option.update({
      isDeleted: true,
      deletedBy: userId,
    });

    return { message: 'Option deleted successfully' };
  }

  // Company Question Mapping operations
  async getCompanyQuestions(companyId: string, languageCode: string = 'en') {
    // Verify company exists
    const company = await this.companyModel.findOne({
      where: { id: companyId, isDeleted: false },
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }

    // Get all questions mapped to this company
    const mappings = await this.companyQuestionMapModel.findAll({
      where: {
        company_id: companyId,
        isDeleted: false,
      },
      include: [
        {
          model: Question,
          include: [
            {
              model: QuestionOption,
              where: { isDeleted: false },
              required: false,
            },
          ],
        },
      ],
    });

    const questions = mappings.map(mapping => mapping.question);

    // Localize the questions
    return this.localizeQuestions(questions, languageCode);
  }

  async assignQuestionsToCompany(companyId: string, assignDto: AssignCompanyQuestionDto) {
    // Verify company exists
    const company = await this.companyModel.findOne({
      where: { id: companyId, isDeleted: false },
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }

    const transaction = await this.sequelize.transaction();

    try {
      // Verify all questions exist
      for (const questionId of assignDto.questionIds) {
        await this.findQuestionById(questionId);
      }

      // Create mappings for each question
      const mappings: CompanyQuestionMap[] = [];
      for (const questionId of assignDto.questionIds) {
        // Check if mapping already exists
        const existingMapping = await this.companyQuestionMapModel.findOne({
          where: {
            company_id: companyId,
            question_id: questionId,
            isDeleted: false,
          },
          transaction,
        });

        if (!existingMapping) {
          const mapping = await this.companyQuestionMapModel.create(
            {
              company_id: companyId,
              question_id: questionId,
              createdBy: assignDto.createdBy || '',
            } as CreationAttributes<CompanyQuestionMap>,
            { transaction },
          );
          mappings.push(mapping);
        }
      }

      await transaction.commit();
      return { message: `${mappings.length} questions assigned to company successfully` };
    } catch (error) {
      await transaction.rollback();
      throw new BadRequestException(`Failed to assign questions: ${error.message}`);
    }
  }

  async removeQuestionFromCompany(companyId: string, questionId: string, userId: string) {
    const mapping = await this.companyQuestionMapModel.findOne({
      where: {
        company_id: companyId,
        question_id: questionId,
        isDeleted: false,
      },
    });

    if (!mapping) {
      throw new NotFoundException(`Question ${questionId} is not assigned to company ${companyId}`);
    }

    await mapping.update({
      isDeleted: true,
      deletedBy: userId,
    });

    return { message: 'Question removed from company successfully' };
  }

  // Helper methods for localization
  private async localizeQuestions(questions: Question[], languageCode: string): Promise<PlainQuestion[]> {
    const localizedQuestions: PlainQuestion[] = [];

    for (const question of questions) {
      const localizedQuestion = await this.localizeQuestion(question, languageCode);
      localizedQuestions.push(localizedQuestion);
    }

    return localizedQuestions;
  }

  private async localizeQuestion(question: Question, languageCode: string): Promise<PlainQuestion> {
    // Get localized question text
    const localizedQuestionText = await this.translationService.getLocalizedQuestionText(
      question.id,
      languageCode,
      question.question_text
    );

    // Get localized options
    const localizedOptions: PlainQuestionOption[] = [];
    const simplifiedQuestion = question.get({ plain: true });

    if (simplifiedQuestion.options && simplifiedQuestion.options.length > 0) {
      for (const option of simplifiedQuestion.options) {
        const localizedOptionText = await this.translationService.getLocalizedOptionText(
          option.id,
          languageCode,
          option.option_text
        );

        // Create a new option object with localized text
        const plainOption: PlainQuestionOption = option;
        const localizedOption: PlainQuestionOption = {
          ...plainOption,
          option_text: localizedOptionText
        };
        localizedOptions.push(localizedOption);
      }
    }

    // Create a new question object with localized text and options
    const plainQuestion = question.get({ plain: true }) as PlainQuestion;
    const localizedQuestion: PlainQuestion = {
      ...plainQuestion,
      question_text: localizedQuestionText,
      options: localizedOptions
    };

    return localizedQuestion;
  }

}




